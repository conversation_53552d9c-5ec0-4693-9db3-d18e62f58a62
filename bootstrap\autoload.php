<?php
// bootstrap/autoload.php

// Composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
if (file_exists(__DIR__ . '/../.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
    $dotenv->load();
}

// Set default timezone
date_default_timezone_set($_ENV['APP_TIMEZONE'] ?? 'Asia/Dhaka');

// Set error reporting based on environment
if (($_ENV['APP_ENV'] ?? 'production') === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set memory limit for high performance
ini_set('memory_limit', '512M');

// Enable OPcache if available
if (function_exists('opcache_get_status') && opcache_get_status()) {
    opcache_compile_file(__FILE__);
}
