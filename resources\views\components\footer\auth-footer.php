    </main>
    
    <!-- Footer -->
    <footer class="bg-white border-t mt-auto">
        <div class="max-w-7xl mx-auto py-6 px-4 text-center">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <!-- Logo and Description -->
                <div class="flex items-center space-x-2">
                    <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-purple-600 rounded flex items-center justify-center">
                        <span class="text-white font-bold text-sm">J</span>
                    </div>
                    <span class="text-gray-600 font-semibold">JobSpace</span>
                </div>
                
                <!-- Links -->
                <div class="flex flex-wrap justify-center space-x-6 text-sm">
                    <a href="/jobspace/" class="text-gray-500 hover:text-gray-700 transition">Home</a>
                    <a href="/jobspace/about" class="text-gray-500 hover:text-gray-700 transition">About</a>
                    <a href="/jobspace/contact" class="text-gray-500 hover:text-gray-700 transition">Contact</a>
                    <a href="/jobspace/terms" class="text-gray-500 hover:text-gray-700 transition">Terms</a>
                    <a href="/jobspace/privacy" class="text-gray-500 hover:text-gray-700 transition">Privacy</a>
                </div>
                
                <!-- Copyright -->
                <div class="text-gray-500 text-sm">
                    &copy; <?= date('Y') ?> JobSpace. All rights reserved.
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Mobile Menu JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-hide flash messages after 5 seconds
            const flashMessages = document.querySelectorAll('.alert-slide-in');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 500);
                }, 5000);
            });
            
            // Form validation helpers
            window.showLoading = function(button, text = 'Loading...') {
                button.disabled = true;
                button.innerHTML = '<div class="spinner inline-block mr-2"></div>' + text;
            };
            
            window.hideLoading = function(button, originalText) {
                button.disabled = false;
                button.innerHTML = originalText;
            };
            
            // Global error handler
            window.showError = function(message) {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'fixed top-0 left-0 right-0 z-50 bg-red-50 border-l-4 border-red-400 p-4 alert-slide-in';
                errorDiv.innerHTML = `
                    <div class="flex max-w-7xl mx-auto">
                        <div class="flex-shrink-0">
                            <span class="text-red-400 text-xl">❌</span>
                        </div>
                        <div class="ml-3">
                            <p class="text-red-700">${message}</p>
                        </div>
                    </div>
                `;
                document.body.appendChild(errorDiv);
                
                setTimeout(function() {
                    errorDiv.remove();
                }, 5000);
            };
            
            // Global success handler
            window.showSuccess = function(message) {
                const successDiv = document.createElement('div');
                successDiv.className = 'fixed top-0 left-0 right-0 z-50 bg-green-50 border-l-4 border-green-400 p-4 alert-slide-in';
                successDiv.innerHTML = `
                    <div class="flex max-w-7xl mx-auto">
                        <div class="flex-shrink-0">
                            <span class="text-green-400 text-xl">✅</span>
                        </div>
                        <div class="ml-3">
                            <p class="text-green-700">${message}</p>
                        </div>
                    </div>
                `;
                document.body.appendChild(successDiv);
                
                setTimeout(function() {
                    successDiv.remove();
                }, 5000);
            };
        });
    </script>
</body>
</html>
