# Auth Module

Complete authentication and authorization module for JobSpace platform with role-based access control.

## Features

- ✅ Multi-step registration process
- ✅ Email verification with OTP
- ✅ Role-based authentication (User, Business, Creator, Admin)
- ✅ Password reset functionality
- ✅ Welcome email system
- ✅ Referral system
- ✅ Profile picture upload
- ✅ Session management
- ✅ CSRF protection
- ✅ Rate limiting
- ✅ Remember me functionality
- ✅ Role-based redirection
- ✅ Capability-based permissions
- ✅ Middleware support

## User Roles

### 1. User (Regular User)
- **Description**: Browse content, participate in quizzes, shop products, social media
- **Permissions**: browse, participate, shop, social, apply_jobs
- **Redirect**: `/jobspace/feed` (fallback: `/jobspace/dashboard/user`)

### 2. Business (Business Account)
- **Description**: Create and manage advertisements only
- **Permissions**: browse, create_ads, manage_ads
- **Redirect**: `/jobspace/dashboard/creator`

### 3. Creator (Content Creator)
- **Description**: Create quizzes, products, jobs, and content
- **Permissions**: browse, create_quizzes, create_products, create_jobs, create_content
- **Redirect**: `/jobspace/dashboard/creator`

### 4. Ad<PERSON> (Administrator)
- **Description**: Full platform access & management
- **Permissions**: all
- **Redirect**: `/jobspace/dashboard/admin`

## Directory Structure

```
app/modules/auth/
├── config/
│   └── auth.php              # Authentication configuration
├── controllers/
│   └── AuthController.php    # Main authentication controller
├── middleware/
│   └── RoleMiddleware.php    # Role-based access control
├── models/
│   └── User.php              # User model
├── routes/
│   └── web.php               # Authentication routes
├── services/
│   ├── AuthService.php       # Authentication business logic
│   ├── AuthHelper.php        # Authentication helper methods
│   └── EmailService.php      # Email service for notifications
├── views/
│   ├── emails/               # Email templates
│   └── pages/                # Authentication pages
├── module.json               # Module configuration
├── README.md                 # This file
└── test-auth.php            # Testing utilities

```

## Usage

### Basic Authentication Check

```php
use App\Modules\Auth\Services\AuthHelper;

// Check if user is logged in
if (AuthHelper::isLoggedIn()) {
    $user = AuthHelper::user();
    echo "Welcome, " . AuthHelper::getUserName();
}
```

### Role-based Access Control

```php
// Check specific role
if (AuthHelper::isAdmin()) {
    // Admin only code
}

if (AuthHelper::isBusiness()) {
    // Business only code
}

if (AuthHelper::isCreator()) {
    // Creator only code
}

// Check multiple roles
if (AuthHelper::hasAnyRole(['Creator', 'Admin'])) {
    // Creator or Admin code
}
```

### Capability-based Permissions

```php
// Check specific capabilities
if (AuthHelper::canCreateAds()) {
    // Show ad creation interface
}

if (AuthHelper::canCreateContent()) {
    // Show content creation tools
}

// Check any capability
if (AuthHelper::can('manage_users')) {
    // User management code
}
```

### Middleware Usage

```php
use App\Modules\Auth\Middleware\RoleMiddleware;

// Require authentication
RoleMiddleware::requireUser();

// Require specific role
RoleMiddleware::requireAdmin();
RoleMiddleware::requireCreator();
RoleMiddleware::requireBusiness();

// Require specific permissions
RoleMiddleware::requireAdPermission();
RoleMiddleware::requireContentPermission();

// Custom role check
RoleMiddleware::handle(['Creator', 'Admin']);
```

### Page Protection

```php
// Protect entire page
RoleMiddleware::checkPageAccess('admin_dashboard');
RoleMiddleware::checkPageAccess('create_ad');
RoleMiddleware::checkPageAccess('user_dashboard');

// Protect API endpoints
RoleMiddleware::protectAPI(['Admin']);

// Check module access
RoleMiddleware::checkModuleAccess('admin');
```

## Configuration

### Role Configuration (config/auth.php)

```php
'roles' => [
    'User' => [
        'title' => 'Regular User',
        'description' => 'Browse content, participate in quizzes, shop products',
        'icon' => '👤',
        'color' => 'blue',
        'permissions' => ['browse', 'participate', 'shop', 'social'],
        'redirect' => '/jobspace/feed',
        'fallback_redirect' => '/jobspace/dashboard/user'
    ],
    // ... other roles
]
```

### Email Configuration

```php
'email_templates' => [
    'verification' => 'auth/emails/verification',
    'welcome' => 'auth/emails/welcome',
    'password_reset' => 'auth/emails/password_reset'
]
```

## Routes

- `GET /auth/login` - Login page
- `POST /auth/process-login` - Process login
- `GET /auth/register` - Registration step 1
- `GET /auth/register/step2` - Registration step 2
- `POST /auth/process-step1` - Process registration step 1
- `POST /auth/process-step2` - Process registration step 2
- `GET /auth/verify-otp` - OTP verification page
- `POST /auth/process-otp` - Process OTP verification
- `GET /auth/forgot-password` - Forgot password page
- `GET /auth/logout` - Logout
- `GET /auth/check-role` - Check current user role (API)

## Testing

Visit `/jobspace/app/modules/auth/test-auth.php` to test the authentication system.

## Integration with Other Modules

### Feed Module Integration
```php
// In feed module
use App\Modules\Auth\Services\AuthHelper;

if (AuthHelper::isLoggedIn()) {
    // Show personalized feed
} else {
    // Show public content or redirect to login
}
```

### Dashboard Module Integration
```php
// Role-based dashboard routing
$dashboardUrl = AuthHelper::getDashboardUrl();
header("Location: {$dashboardUrl}");
```

## Security Features

- Password hashing with PHP's `password_hash()`
- CSRF token protection
- Session regeneration
- Rate limiting for login attempts
- Secure cookie settings
- Input validation and sanitization
- SQL injection prevention with prepared statements

## Email Templates

- **Verification Email**: Sent during registration with OTP
- **Welcome Email**: Sent after successful verification
- **Password Reset**: Sent for password reset requests

## Error Handling

The module includes comprehensive error handling with user-friendly messages and proper logging.

## Future Enhancements

- Two-factor authentication (2FA)
- Social login integration
- Advanced password policies
- Account lockout mechanisms
- Audit logging
- API token authentication
