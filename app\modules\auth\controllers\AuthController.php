<?php

namespace App\Modules\Auth\Controllers;

use App\Modules\Auth\Services\AuthService;
use App\Modules\Auth\Services\EmailService;
use App\Modules\Auth\Models\User;
use App\Core\SessionManager;
use App\Core\Security;

class AuthController
{
    private AuthService $authService;
    private EmailService $emailService;
    private string $modulePath;

    public function __construct()
    {
        $this->authService = new AuthService();
        $this->emailService = new EmailService();
        $this->modulePath = BASE_PATH . '/app/modules/auth';
    }

    /**
     * Show login page
     */
    public function login()
    {
        return $this->view('pages/login');
    }

    /**
     * Show registration step 1
     */
    public function register()
    {
        return $this->view('pages/register-step1');
    }

    /**
     * Show registration step 2
     */
    public function registerStep2()
    {
        // Check if step 1 is completed
        if (!isset($_SESSION['registration_step1'])) {
            header('Location: /jobspace/auth/register');
            exit;
        }

        $data = [
            'step1_data' => $_SESSION['registration_step1'],
            'available_roles' => User::getAvailableRoles()
        ];

        return $this->view('pages/register-step2', $data);
    }

    /**
     * Process registration step 1
     */
    public function processStep1()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /jobspace/auth/register');
            exit;
        }

        $data = $_POST;
        $errors = $this->authService->validateStep1($data);

        if (!empty($errors)) {
            $_SESSION['registration_errors'] = $errors;
            $_SESSION['registration_data'] = $data;
            header('Location: /jobspace/auth/register');
            exit;
        }

        // Store step 1 data in session
        $_SESSION['registration_step1'] = $data;
        unset($_SESSION['registration_errors'], $_SESSION['registration_data']);

        header('Location: /jobspace/auth/register/step2');
        exit;
    }

    /**
     * Process registration step 2 and send verification
     */
    public function processStep2()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /jobspace/auth/register/step2');
            exit;
        }

        if (!isset($_SESSION['registration_step1'])) {
            header('Location: /jobspace/auth/register');
            exit;
        }

        $step1Data = $_SESSION['registration_step1'];
        $step2Data = $_POST;

        $errors = $this->authService->validateStep2($step2Data);

        if (!empty($errors)) {
            $_SESSION['registration_errors'] = $errors;
            $_SESSION['registration_data'] = $step2Data;
            header('Location: /jobspace/auth/register/step2');
            exit;
        }

        // Process registration
        $result = $this->authService->processRegistration($step1Data, $step2Data);

        if ($result['success']) {
            // Send verification email
            $name = $step1Data['first_name'] . ' ' . $step1Data['last_name'];
            $this->emailService->sendVerificationEmail(
                $result['email'],
                $name,
                $result['verification_token'],
                $result['otp']
            );

            // Clean up session
            unset($_SESSION['registration_step1'], $_SESSION['registration_errors'], $_SESSION['registration_data']);

            // Redirect to verification page
            $_SESSION['verification_email'] = $result['email'];
            header('Location: /jobspace/auth/verify-otp');
            exit;
        }

        $_SESSION['registration_errors'] = ['general' => 'Registration failed. Please try again.'];
        header('Location: /jobspace/auth/register/step2');
        exit;
    }

    /**
     * Show OTP verification page
     */
    public function verifyOTP()
    {
        if (!isset($_SESSION['verification_email'])) {
            header('Location: /jobspace/auth/register');
            exit;
        }

        $data = [
            'email' => $_SESSION['verification_email']
        ];

        return $this->view('pages/verify-otp', $data);
    }

    /**
     * Process OTP verification
     */
    public function processOTPVerification()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /jobspace/auth/verify-otp');
            exit;
        }

        if (!isset($_SESSION['verification_email'])) {
            header('Location: /jobspace/auth/register');
            exit;
        }

        $email = $_SESSION['verification_email'];
        $otp = $_POST['otp'] ?? '';

        $result = $this->authService->verifyOTP($email, $otp);

        if ($result['success']) {
            // Send welcome email
            $userData = $result['user'];
            $name = $userData['first_name'] . ' ' . $userData['last_name'];
            $this->emailService->sendWelcomeEmail($email, $name, $userData);

            // Clean up session
            unset($_SESSION['verification_email']);

            // Auto login if configured
            $_SESSION['user'] = $userData;
            $_SESSION['success_message'] = 'Account created successfully! Welcome to JobSpace.';

            // Role-based redirection after registration
            $redirectUrl = $this->getRedirectUrlByRole($userData['role']);
            header("Location: {$redirectUrl}");
            exit;
        }

        $_SESSION['verification_error'] = $result['message'];
        header('Location: /jobspace/auth/verify-otp');
        exit;
    }

    /**
     * Resend OTP
     */
    public function resendOTP()
    {
        if (!isset($_SESSION['verification_email'])) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'No verification in progress']);
            exit;
        }

        $email = $_SESSION['verification_email'];
        $verificationData = User::getVerificationData($email);

        if (!$verificationData) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Verification data not found']);
            exit;
        }

        // Generate new OTP
        $newOTP = User::generateOTP();
        $verificationData['otp'] = $newOTP;
        $verificationData['otp_expires'] = time() + 600; // 10 minutes

        User::storeVerificationData($email, $verificationData);

        // Send new OTP
        $userData = $verificationData['user_data'];
        $name = $userData['first_name'] . ' ' . $userData['last_name'];
        $this->emailService->sendOTPEmail($email, $name, $newOTP);

        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'New OTP sent successfully']);
        exit;
    }

    /**
     * Check email availability (AJAX)
     */
    public function checkEmailAvailability()
    {
        $email = $_GET['email'] ?? '';
        
        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            header('Content-Type: application/json');
            echo json_encode(['available' => false, 'message' => 'Invalid email format']);
            exit;
        }

        $available = User::isEmailAvailable($email);
        
        header('Content-Type: application/json');
        echo json_encode([
            'available' => $available,
            'message' => $available ? 'Email is available' : 'Email is already registered'
        ]);
        exit;
    }

    /**
     * Check username availability (AJAX)
     */
    public function checkUsernameAvailability()
    {
        $username = $_GET['username'] ?? '';

        if (empty($username) || strlen($username) < 3) {
            header('Content-Type: application/json');
            echo json_encode(['available' => false, 'message' => 'Username must be at least 3 characters']);
            exit;
        }

        $available = User::isUsernameAvailable($username);

        header('Content-Type: application/json');
        echo json_encode([
            'available' => $available,
            'message' => $available ? 'Username is available' : 'Username is already taken'
        ]);
        exit;
    }

    /**
     * Process login
     */
    public function processLogin()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /jobspace/auth/login');
            exit;
        }

        $login = Security::sanitizeInput($_POST['login'] ?? '');
        $password = $_POST['password'] ?? '';
        $rememberMe = isset($_POST['remember_me']);
        $clientIP = Security::getClientIP();

        // Basic validation
        if (empty($login) || empty($password)) {
            SessionManager::flash('login_error', 'Please enter both email/username and password');
            SessionManager::set('login_data', $_POST);
            header('Location: /jobspace/auth/login');
            exit;
        }

        // Check login attempts
        $attemptCheck = Security::checkLoginAttempts($login, $clientIP);
        if (!$attemptCheck['allowed']) {
            SessionManager::flash('login_error', $attemptCheck['reason']);
            header('Location: /jobspace/auth/login');
            exit;
        }

        // Find user by email or username
        $user = User::findByEmail($login);
        if (!$user) {
            $user = User::findByUsername($login);
        }

        // Verify credentials
        if (!$user || !Security::verifyPassword($password, $user['password'])) {
            Security::recordFailedLogin($login, $clientIP);
            SessionManager::flash('login_error', 'Invalid email/username or password');
            SessionManager::set('login_data', ['login' => $login, 'remember_me' => $rememberMe]);
            header('Location: /jobspace/auth/login');
            exit;
        }

        // Check if user is verified
        if (!$user['email_verified']) {
            SessionManager::flash('login_error', 'Please verify your email address before logging in');
            header('Location: /jobspace/auth/login');
            exit;
        }

        // Check if user is active
        if ($user['status'] !== 'active') {
            SessionManager::flash('login_error', 'Your account is not active. Please contact support');
            header('Location: /jobspace/auth/login');
            exit;
        }

        // Clear failed login attempts
        Security::clearLoginAttempts($login, $clientIP);

        // Update last login
        User::updateLastLogin($user['id']);

        // Login user
        SessionManager::login($user);

        // Set remember me cookie if requested
        if ($rememberMe) {
            $this->setRememberMeCookie($user['id']);
        }

        SessionManager::flash('success_message', 'Welcome back, ' . $user['first_name'] . '!');

        // Role-based redirection
        $redirectUrl = $this->getRedirectUrlByRole($user['role']);
        header("Location: {$redirectUrl}");
        exit;
    }

    /**
     * Show forgot password page
     */
    public function forgotPassword()
    {
        return $this->view('pages/forgot-password');
    }

    /**
     * Process forgot password
     */
    public function processForgotPassword()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /jobspace/auth/forgot-password');
            exit;
        }

        $email = $_POST['email'] ?? '';

        if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $_SESSION['forgot_error'] = 'Please enter a valid email address';
            header('Location: /jobspace/auth/forgot-password');
            exit;
        }

        // For now, just show success message
        $_SESSION['forgot_success'] = 'Password reset instructions sent to your email (functionality will be implemented with database)';
        header('Location: /jobspace/auth/forgot-password');
        exit;
    }

    /**
     * Show reset password page
     */
    public function resetPassword()
    {
        $token = $_GET['token'] ?? '';

        if (empty($token)) {
            $_SESSION['login_error'] = 'Invalid reset token';
            header('Location: /jobspace/auth/login');
            exit;
        }

        $data = ['token' => $token];
        return $this->view('pages/reset-password', $data);
    }

    /**
     * Process reset password
     */
    public function processResetPassword()
    {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: /jobspace/auth/login');
            exit;
        }

        // For now, just show success message
        $_SESSION['success_message'] = 'Password reset functionality will be implemented with database integration';
        header('Location: /jobspace/auth/login');
        exit;
    }

    /**
     * Verify email via link
     */
    public function verifyEmail()
    {
        $token = $_GET['token'] ?? '';

        if (empty($token)) {
            $_SESSION['login_error'] = 'Invalid verification token';
            header('Location: /jobspace/auth/login');
            exit;
        }

        // For now, just show success message
        $_SESSION['success_message'] = 'Email verification via link will be implemented with database integration';
        header('Location: /jobspace/auth/login');
        exit;
    }

    /**
     * Show dashboard
     */
    public function dashboard()
    {
        // Check if user is logged in (for now, just show dashboard)
        return $this->view('pages/dashboard');
    }

    /**
     * Set remember me cookie
     */
    private function setRememberMeCookie(int $userId): void
    {
        $token = Security::generateRandomString(64);
        $expires = time() + (30 * 24 * 60 * 60); // 30 days

        // Store token in database (you'd need a remember_tokens table)
        // For now, just set a secure cookie
        setcookie(
            'remember_token',
            $token,
            $expires,
            '/',
            '',
            isset($_SERVER['HTTPS']),
            true
        );
    }

    /**
     * Logout
     */
    public function logout()
    {
        // Clear remember me cookie
        setcookie('remember_token', '', time() - 3600, '/');

        SessionManager::logout();
        SessionManager::flash('success_message', 'You have been logged out successfully');
        header('Location: /jobspace/auth/login');
        exit;
    }

    /**
     * Get redirect URL based on user role
     */
    private function getRedirectUrlByRole(string $role): string
    {
        // Load auth config
        $authConfig = include BASE_PATH . '/app/modules/auth/config/auth.php';

        // Get role configuration
        $roleConfig = $authConfig['roles'][$role] ?? null;

        if (!$roleConfig) {
            return '/jobspace/dashboard/user';
        }

        $redirectUrl = $roleConfig['redirect'];
        $fallbackUrl = $roleConfig['fallback_redirect'];

        // Role-specific logic
        switch ($role) {
            case 'User':
                // Check if feed module exists
                if ($redirectUrl === '/jobspace/feed' && !file_exists(BASE_PATH . '/app/modules/feed')) {
                    return $fallbackUrl;
                }
                return $redirectUrl;

            case 'Business':
                // Business users go to creator dashboard (for ads management)
                return $redirectUrl;

            case 'Creator':
                // Creators go to creator dashboard
                return $redirectUrl;

            case 'Admin':
                // Admins go to admin dashboard
                return $redirectUrl;

            default:
                return $fallbackUrl ?? '/jobspace/dashboard/user';
        }
    }

    /**
     * Render view
     */
    private function view(string $viewPath, array $data = []): string
    {
        $viewFile = $this->modulePath . '/views/' . $viewPath . '.php';

        if (!file_exists($viewFile)) {
            return "View not found: {$viewPath}";
        }

        // Extract data to variables
        extract($data);

        // Start output buffering
        ob_start();

        // Include view file
        include $viewFile;

        // Return rendered content
        return ob_get_clean();
    }
}
