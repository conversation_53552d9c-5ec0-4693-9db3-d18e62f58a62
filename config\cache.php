<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Cache Store
    |--------------------------------------------------------------------------
    */
    'default' => env('CACHE_DRIVER', 'file'),

    /*
    |--------------------------------------------------------------------------
    | Cache Stores
    |--------------------------------------------------------------------------
    */
    'stores' => [
        'file' => [
            'driver' => 'file',
            'path' => env('CACHE_PATH', BASE_PATH . '/storage/cache'),
            'permissions' => 0755,
        ],

        'database' => [
            'driver' => 'database',
            'table' => 'cache_entries',
            'connection' => null,
        ],

        'redis' => [
            'driver' => 'redis',
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'port' => env('REDIS_PORT', 6379),
            'password' => env('REDIS_PASSWORD', null),
            'database' => env('REDIS_CACHE_DB', 1),
            'prefix' => env('REDIS_PREFIX', 'jobspace_cache'),
        ],

        'memcached' => [
            'driver' => 'memcached',
            'servers' => [
                [
                    'host' => env('MEMCACHED_HOST', '127.0.0.1'),
                    'port' => env('MEMCACHED_PORT', 11211),
                    'weight' => 100,
                ],
            ],
            'prefix' => env('MEMCACHED_PREFIX', 'jobspace_cache'),
        ],

        'array' => [
            'driver' => 'array',
            'serialize' => false,
        ],

        'null' => [
            'driver' => 'null',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Key Prefix
    |--------------------------------------------------------------------------
    */
    'prefix' => env('CACHE_PREFIX', 'jobspace_cache'),

    /*
    |--------------------------------------------------------------------------
    | Default Cache TTL
    |--------------------------------------------------------------------------
    */
    'ttl' => env('CACHE_TTL', 3600), // 1 hour

    /*
    |--------------------------------------------------------------------------
    | Cache Tags
    |--------------------------------------------------------------------------
    */
    'tags' => [
        'enabled' => true,
        'separator' => ':',
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'compression' => env('CACHE_COMPRESSION', true),
        'serialization' => env('CACHE_SERIALIZATION', 'php'), // php, json, igbinary
        'max_size' => env('CACHE_MAX_SIZE', '100M'),
        'cleanup_probability' => env('CACHE_CLEANUP_PROBABILITY', 2),
        'cleanup_divisor' => env('CACHE_CLEANUP_DIVISOR', 100),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Layers
    |--------------------------------------------------------------------------
    */
    'layers' => [
        'enabled' => env('CACHE_LAYERS_ENABLED', true),
        'l1' => 'array',    // In-memory cache
        'l2' => 'file',     // Persistent cache
        'l3' => 'database', // Database cache
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Warming
    |--------------------------------------------------------------------------
    */
    'warming' => [
        'enabled' => env('CACHE_WARMING_ENABLED', false),
        'schedule' => '0 */6 * * *', // Every 6 hours
        'keys' => [
            'system.config',
            'user.permissions',
            'quiz.categories',
            'job.categories',
            'product.categories',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Monitoring
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'enabled' => env('CACHE_MONITORING_ENABLED', true),
        'hit_rate_threshold' => 0.8, // 80%
        'memory_threshold' => 0.9,   // 90%
        'log_slow_operations' => true,
        'slow_threshold' => 100, // milliseconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Distributed Cache
    |--------------------------------------------------------------------------
    */
    'distributed' => [
        'enabled' => env('CACHE_DISTRIBUTED_ENABLED', false),
        'consistency' => 'eventual', // strong, eventual
        'replication_factor' => 2,
        'nodes' => [
            'node1' => [
                'host' => env('CACHE_NODE1_HOST', '127.0.0.1'),
                'port' => env('CACHE_NODE1_PORT', 6379),
            ],
            'node2' => [
                'host' => env('CACHE_NODE2_HOST', '127.0.0.1'),
                'port' => env('CACHE_NODE2_PORT', 6380),
            ],
        ],
    ],
];
