<?php

/**
 * Database Migration Script
 * Creates all necessary tables for the JobSpace application
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Define BASE_PATH
define('BASE_PATH', dirname(__DIR__));

// Load environment variables
if (file_exists(BASE_PATH . '/.env')) {
    $lines = file(BASE_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

use App\Core\Database;

try {
    echo "Starting database migration...\n\n";

    // Create tables using Database class
    Database::createTables();

    echo "✅ Database migration completed successfully!\n\n";

    echo "Created tables:\n";
    echo "- users\n";
    echo "- verification_tokens\n";
    echo "- password_resets\n";
    echo "- sessions\n";
    echo "- login_attempts\n\n";

    // Check if admin user exists
    try {
        $adminExists = Database::fetch("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'");

        if (($adminExists['count'] ?? 0) == 0) {
            echo "ℹ️  No admin user found. You can create an admin account during registration.\n";
        } else {
            echo "✅ Admin user already exists.\n";
        }
    } catch (Exception $e) {
        echo "ℹ️  Could not check for admin user (table may be empty).\n";
    }

    echo "\n🎉 Database is ready for use!\n";

} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    echo "\nPlease check your database configuration in config/database.php\n";
    echo "Make sure your database server is running and accessible.\n";
    exit(1);
}
