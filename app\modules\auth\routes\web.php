<?php

/**
 * Auth Module Routes
 * Authentication related routes
 */

use App\Modules\Auth\Controllers\AuthController;

// Initialize controller
$controller = new AuthController();

// Auth routes
return [
    // Login routes
    '/auth/login' => [$controller, 'login'],
    '/auth/process-login' => [$controller, 'processLogin'],
    
    // Registration routes
    '/auth/register' => [$controller, 'register'],
    '/auth/register/step2' => [$controller, 'registerStep2'],
    '/auth/process-step1' => [$controller, 'processStep1'],
    '/auth/process-step2' => [$controller, 'processStep2'],
    
    // Verification routes
    '/auth/verify-otp' => [$controller, 'verifyOTP'],
    '/auth/process-otp' => [$controller, 'processOTPVerification'],
    '/auth/resend-otp' => [$controller, 'resendOTP'],
    '/auth/verify-email' => [$controller, 'verifyEmail'],
    
    // Password reset routes
    '/auth/forgot-password' => [$controller, 'forgotPassword'],
    '/auth/process-forgot-password' => [$controller, 'processForgotPassword'],
    '/auth/reset-password' => [$controller, 'resetPassword'],
    '/auth/process-reset-password' => [$controller, 'processResetPassword'],
    
    // AJAX routes
    '/auth/check-email' => [$controller, 'checkEmailAvailability'],
    '/auth/check-username' => [$controller, 'checkUsernameAvailability'],

    // Dashboard (temporary - will be handled by dashboard module)
    '/dashboard' => [$controller, 'dashboard'],

    // Logout
    '/auth/logout' => [$controller, 'logout'],

    // Role-based redirects (for testing)
    '/auth/redirect-test' => function() {
        if (!isset($_SESSION['user'])) {
            header('Location: /jobspace/auth/login');
            exit;
        }

        $authController = new AuthController();
        $role = $_SESSION['user']['role'] ?? 'User';
        $redirectMethod = new ReflectionMethod($authController, 'getRedirectUrlByRole');
        $redirectMethod->setAccessible(true);
        $redirectUrl = $redirectMethod->invoke($authController, $role);

        header("Location: {$redirectUrl}");
        exit;
    },
];
