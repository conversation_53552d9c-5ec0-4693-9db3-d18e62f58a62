<?php

namespace App\Modules\Auth\Services;

use App\Modules\Auth\Services\AuthService;
use App\Core\SessionManager;

class AuthHelper
{
    /**
     * Check if current user is logged in
     */
    public static function isLoggedIn(): bool
    {
        return AuthService::isAuthenticated();
    }

    /**
     * Get current user
     */
    public static function user(): ?array
    {
        return AuthService::getCurrentUser();
    }

    /**
     * Get current user role
     */
    public static function userRole(): ?string
    {
        $user = self::user();
        return $user['role'] ?? null;
    }

    /**
     * Check if current user has specific role
     */
    public static function hasRole(string $role): bool
    {
        return self::userRole() === $role;
    }

    /**
     * Check if current user has any of the given roles
     */
    public static function hasAnyRole(array $roles): bool
    {
        $userRole = self::userRole();
        return $userRole && in_array($userRole, $roles);
    }

    /**
     * Check if current user is admin
     */
    public static function isAdmin(): bool
    {
        return self::hasRole('Admin');
    }

    /**
     * Check if current user is creator
     */
    public static function isCreator(): bool
    {
        return self::hasRole('Creator');
    }

    /**
     * Check if current user is business
     */
    public static function isBusiness(): bool
    {
        return self::hasRole('Business');
    }

    /**
     * Check if current user is regular user
     */
    public static function isUser(): bool
    {
        return self::hasRole('User');
    }

    /**
     * Check if user can create ads
     */
    public static function canCreateAds(): bool
    {
        return self::hasAnyRole(['Business', 'Admin']);
    }

    /**
     * Check if user can create content
     */
    public static function canCreateContent(): bool
    {
        return self::hasAnyRole(['Creator', 'Admin']);
    }

    /**
     * Check if user can manage platform
     */
    public static function canManagePlatform(): bool
    {
        return self::isAdmin();
    }

    /**
     * Get user's dashboard URL
     */
    public static function getDashboardUrl(): string
    {
        $role = self::userRole();
        
        $authConfig = include BASE_PATH . '/app/modules/auth/config/auth.php';
        $roleConfig = $authConfig['roles'][$role] ?? null;
        
        if ($roleConfig) {
            $redirectUrl = $roleConfig['redirect'];
            $fallbackUrl = $roleConfig['fallback_redirect'];
            
            // For User role, check if feed module exists
            if ($role === 'User' && $redirectUrl === '/jobspace/feed') {
                if (!file_exists(BASE_PATH . '/app/modules/feed')) {
                    return $fallbackUrl;
                }
            }
            
            return $redirectUrl;
        }
        
        return '/jobspace/dashboard/user';
    }

    /**
     * Redirect to appropriate dashboard
     */
    public static function redirectToDashboard(): void
    {
        $dashboardUrl = self::getDashboardUrl();
        header("Location: {$dashboardUrl}");
        exit;
    }

    /**
     * Require authentication
     */
    public static function requireAuth(string $redirectUrl = '/jobspace/auth/login'): void
    {
        if (!self::isLoggedIn()) {
            SessionManager::flash('login_error', 'Please login to access this page');
            header("Location: {$redirectUrl}");
            exit;
        }
    }

    /**
     * Require specific role
     */
    public static function requireRole(string $role, string $redirectUrl = '/jobspace/auth/login'): void
    {
        self::requireAuth($redirectUrl);
        
        if (!self::hasRole($role)) {
            SessionManager::flash('error', 'You do not have permission to access this page');
            self::redirectToDashboard();
        }
    }

    /**
     * Require any of the given roles
     */
    public static function requireAnyRole(array $roles, string $redirectUrl = '/jobspace/auth/login'): void
    {
        self::requireAuth($redirectUrl);
        
        if (!self::hasAnyRole($roles)) {
            SessionManager::flash('error', 'You do not have permission to access this page');
            self::redirectToDashboard();
        }
    }

    /**
     * Require admin access
     */
    public static function requireAdmin(): void
    {
        self::requireRole('Admin');
    }

    /**
     * Require creator access
     */
    public static function requireCreator(): void
    {
        self::requireRole('Creator');
    }

    /**
     * Require business access
     */
    public static function requireBusiness(): void
    {
        self::requireRole('Business');
    }

    /**
     * Require ad creation permission
     */
    public static function requireAdPermission(): void
    {
        self::requireAnyRole(['Business', 'Admin']);
    }

    /**
     * Require content creation permission
     */
    public static function requireContentPermission(): void
    {
        self::requireAnyRole(['Creator', 'Admin']);
    }

    /**
     * Get user capabilities
     */
    public static function getCapabilities(): array
    {
        return AuthService::getUserCapabilities();
    }

    /**
     * Check if user can perform action
     */
    public static function can(string $action): bool
    {
        $capabilities = self::getCapabilities();
        return $capabilities[$action] ?? false;
    }

    /**
     * Get role display information
     */
    public static function getRoleInfo(string $role = null): ?array
    {
        $role = $role ?? self::userRole();
        if (!$role) {
            return null;
        }
        
        $authConfig = include BASE_PATH . '/app/modules/auth/config/auth.php';
        return $authConfig['roles'][$role] ?? null;
    }

    /**
     * Get user's full name
     */
    public static function getUserName(): string
    {
        $user = self::user();
        if (!$user) {
            return 'Guest';
        }
        
        return trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')) ?: $user['username'] ?? 'User';
    }

    /**
     * Get user's avatar URL
     */
    public static function getAvatarUrl(): string
    {
        $user = self::user();
        if (!$user) {
            return '/jobspace/public/assets/images/default-avatar.png';
        }
        
        return $user['profile_picture'] ?? '/jobspace/public/assets/images/default-avatar.png';
    }

    /**
     * Logout current user
     */
    public static function logout(): void
    {
        SessionManager::logout();
        SessionManager::flash('success_message', 'You have been logged out successfully');
        header('Location: /jobspace/auth/login');
        exit;
    }
}
