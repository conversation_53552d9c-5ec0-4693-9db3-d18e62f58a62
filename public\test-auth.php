<?php

/**
 * Authentication System Test Script
 * Tests all auth functionality
 */

// Define BASE_PATH
define('BASE_PATH', dirname(__DIR__));

// Load environment variables
if (file_exists(BASE_PATH . '/.env')) {
    $lines = file(BASE_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Load autoloader
require_once BASE_PATH . '/vendor/autoload.php';

// Load core classes
require_once BASE_PATH . '/app/core/Database.php';
require_once BASE_PATH . '/app/core/SessionManager.php';
require_once BASE_PATH . '/app/core/Security.php';

use App\Core\Database;
use App\Core\SessionManager;
use App\Core\Security;
use App\Modules\Auth\Models\User;
use App\Modules\Auth\Services\AuthService;
use App\Modules\Auth\Services\EmailService;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobSpace Auth System Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 20px auto; padding: 20px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .warning { color: #856404; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .test-section { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 8px; }
        .test-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; }
    </style>
</head>
<body>
    <h1>🧪 JobSpace Authentication System Test</h1>
    
    <?php
    $tests = [];
    $passed = 0;
    $failed = 0;
    
    // Test 1: Database Connection
    echo '<div class="test-section">';
    echo '<div class="test-title">1. Database Connection Test</div>';
    try {
        $connection = Database::getConnection();
        echo '<div class="success">✅ Database connection successful</div>';
        $tests[] = ['name' => 'Database Connection', 'status' => 'passed'];
        $passed++;
    } catch (Exception $e) {
        echo '<div class="error">❌ Database connection failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
        $tests[] = ['name' => 'Database Connection', 'status' => 'failed', 'error' => $e->getMessage()];
        $failed++;
    }
    echo '</div>';
    
    // Test 2: Tables Existence
    echo '<div class="test-section">';
    echo '<div class="test-title">2. Database Tables Test</div>';
    try {
        $tables = ['users', 'verification_tokens', 'password_resets', 'sessions', 'login_attempts'];
        foreach ($tables as $table) {
            $result = Database::fetch("SHOW TABLES LIKE '$table'");
            if ($result) {
                echo '<div class="success">✅ Table `' . $table . '` exists</div>';
            } else {
                echo '<div class="warning">⚠️ Table `' . $table . '` does not exist</div>';
            }
        }
        $tests[] = ['name' => 'Database Tables', 'status' => 'passed'];
        $passed++;
    } catch (Exception $e) {
        echo '<div class="error">❌ Table check failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
        $tests[] = ['name' => 'Database Tables', 'status' => 'failed', 'error' => $e->getMessage()];
        $failed++;
    }
    echo '</div>';
    
    // Test 3: User Model Functions
    echo '<div class="test-section">';
    echo '<div class="test-title">3. User Model Test</div>';
    try {
        // Test email availability
        $emailAvailable = User::isEmailAvailable('<EMAIL>');
        echo '<div class="success">✅ Email availability check: ' . ($emailAvailable ? 'Available' : 'Not Available') . '</div>';
        
        // Test username availability
        $usernameAvailable = User::isUsernameAvailable('testuser');
        echo '<div class="success">✅ Username availability check: ' . ($usernameAvailable ? 'Available' : 'Not Available') . '</div>';
        
        // Test password hashing
        $hashedPassword = User::hashPassword('testpassword123');
        echo '<div class="success">✅ Password hashing works</div>';
        
        // Test password verification
        $passwordVerified = User::verifyPassword('testpassword123', $hashedPassword);
        echo '<div class="success">✅ Password verification: ' . ($passwordVerified ? 'Correct' : 'Failed') . '</div>';
        
        // Test OTP generation
        $otp = User::generateOTP();
        echo '<div class="success">✅ OTP generation: ' . $otp . '</div>';
        
        // Test verification token generation
        $token = User::generateVerificationToken();
        echo '<div class="success">✅ Verification token generation: ' . substr($token, 0, 20) . '...</div>';
        
        $tests[] = ['name' => 'User Model Functions', 'status' => 'passed'];
        $passed++;
    } catch (Exception $e) {
        echo '<div class="error">❌ User model test failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
        $tests[] = ['name' => 'User Model Functions', 'status' => 'failed', 'error' => $e->getMessage()];
        $failed++;
    }
    echo '</div>';
    
    // Test 4: Auth Service
    echo '<div class="test-section">';
    echo '<div class="test-title">4. Auth Service Test</div>';
    try {
        $authService = new AuthService();
        
        // Test step 1 validation
        $step1Data = [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'date_of_birth' => '1990-01-01',
            'gender' => 'Male',
            'password' => 'TestPassword123',
            'confirm_password' => 'TestPassword123'
        ];
        
        $step1Errors = $authService->validateStep1($step1Data);
        if (empty($step1Errors)) {
            echo '<div class="success">✅ Step 1 validation passed</div>';
        } else {
            echo '<div class="warning">⚠️ Step 1 validation errors: ' . implode(', ', $step1Errors) . '</div>';
        }
        
        // Test step 2 validation
        $step2Data = [
            'username' => 'johndoe123',
            'address' => '123 Main St',
            'role' => 'User',
            'terms_accepted' => '1'
        ];
        
        $step2Errors = $authService->validateStep2($step2Data);
        if (empty($step2Errors)) {
            echo '<div class="success">✅ Step 2 validation passed</div>';
        } else {
            echo '<div class="warning">⚠️ Step 2 validation errors: ' . implode(', ', $step2Errors) . '</div>';
        }
        
        $tests[] = ['name' => 'Auth Service', 'status' => 'passed'];
        $passed++;
    } catch (Exception $e) {
        echo '<div class="error">❌ Auth service test failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
        $tests[] = ['name' => 'Auth Service', 'status' => 'failed', 'error' => $e->getMessage()];
        $failed++;
    }
    echo '</div>';
    
    // Test 5: Email Service
    echo '<div class="test-section">';
    echo '<div class="test-title">5. Email Service Test</div>';
    try {
        $emailService = new EmailService();
        
        // Test email template loading
        echo '<div class="info">📧 Email service initialized</div>';
        echo '<div class="info">📧 Email configuration:</div>';
        echo '<pre>';
        echo 'Host: ' . ($_ENV['MAIL_HOST'] ?? 'Not set') . "\n";
        echo 'Port: ' . ($_ENV['MAIL_PORT'] ?? 'Not set') . "\n";
        echo 'Username: ' . ($_ENV['MAIL_USERNAME'] ?? 'Not set') . "\n";
        echo 'From: ' . ($_ENV['MAIL_FROM_ADDRESS'] ?? 'Not set') . "\n";
        echo '</pre>';
        
        if (!empty($_ENV['MAIL_USERNAME']) && !empty($_ENV['MAIL_PASSWORD'])) {
            echo '<div class="success">✅ Email credentials configured</div>';
        } else {
            echo '<div class="warning">⚠️ Email credentials not configured (will log emails instead)</div>';
        }
        
        $tests[] = ['name' => 'Email Service', 'status' => 'passed'];
        $passed++;
    } catch (Exception $e) {
        echo '<div class="error">❌ Email service test failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
        $tests[] = ['name' => 'Email Service', 'status' => 'failed', 'error' => $e->getMessage()];
        $failed++;
    }
    echo '</div>';
    
    // Test 6: Security Functions
    echo '<div class="test-section">';
    echo '<div class="test-title">6. Security Functions Test</div>';
    try {
        // Test CSRF token generation
        $csrfToken = Security::generateCSRFToken();
        echo '<div class="success">✅ CSRF token generated: ' . substr($csrfToken, 0, 20) . '...</div>';
        
        // Test input sanitization
        $sanitized = Security::sanitizeInput('<script>alert("test")</script>');
        echo '<div class="success">✅ Input sanitization works</div>';
        
        // Test email validation
        $emailValid = Security::validateEmail('<EMAIL>');
        echo '<div class="success">✅ Email validation: ' . ($emailValid ? 'Valid' : 'Invalid') . '</div>';
        
        // Test client IP
        $clientIP = Security::getClientIP();
        echo '<div class="success">✅ Client IP detection: ' . $clientIP . '</div>';
        
        $tests[] = ['name' => 'Security Functions', 'status' => 'passed'];
        $passed++;
    } catch (Exception $e) {
        echo '<div class="error">❌ Security functions test failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
        $tests[] = ['name' => 'Security Functions', 'status' => 'failed', 'error' => $e->getMessage()];
        $failed++;
    }
    echo '</div>';
    
    // Test Summary
    echo '<div class="test-section">';
    echo '<div class="test-title">📊 Test Summary</div>';
    echo '<div class="info">';
    echo '<strong>Total Tests:</strong> ' . ($passed + $failed) . '<br>';
    echo '<strong>Passed:</strong> ' . $passed . '<br>';
    echo '<strong>Failed:</strong> ' . $failed . '<br>';
    echo '<strong>Success Rate:</strong> ' . round(($passed / ($passed + $failed)) * 100, 2) . '%';
    echo '</div>';
    
    if ($failed === 0) {
        echo '<div class="success">🎉 All tests passed! Your authentication system is ready to use.</div>';
        echo '<div class="info"><a href="/jobspace/auth/register">Start Registration</a> | <a href="/jobspace/auth/login">Go to Login</a></div>';
    } else {
        echo '<div class="warning">⚠️ Some tests failed. Please check the issues above.</div>';
    }
    echo '</div>';
    ?>
    
    <hr>
    <p><small>JobSpace © 2024 - Authentication System Test</small></p>
</body>
</html>
