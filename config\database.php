<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    */
    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    */
    'connections' => [
        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'jobspace'),
            'username' => env('DB_USERNAME', 'root'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => 'InnoDB',
            'persistent' => false,
            'options' => [
                PDO::ATTR_TIMEOUT => 30,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
                PDO::MYSQL_ATTR_FOUND_ROWS => true,
            ],
        ],

        'mysql_read' => [
            'driver' => 'mysql',
            'host' => env('DB_READ_HOST', env('DB_HOST', '127.0.0.1')),
            'port' => env('DB_READ_PORT', env('DB_PORT', '3306')),
            'database' => env('DB_READ_DATABASE', env('DB_DATABASE', 'jobspace')),
            'username' => env('DB_READ_USERNAME', env('DB_USERNAME', 'root')),
            'password' => env('DB_READ_PASSWORD', env('DB_PASSWORD', '')),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => 'InnoDB',
            'persistent' => false,
        ],

        'sqlite' => [
            'driver' => 'sqlite',
            'database' => env('DB_DATABASE', BASE_PATH . '/database/database.sqlite'),
            'prefix' => '',
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'jobspace'),
            'username' => env('DB_USERNAME', 'postgres'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    */
    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */
    'max_connections' => env('DB_MAX_CONNECTIONS', 100),
    'timeout' => env('DB_TIMEOUT', 30),
    'retry_attempts' => env('DB_RETRY_ATTEMPTS', 3),
    'retry_delay' => env('DB_RETRY_DELAY', 1000), // milliseconds

    /*
    |--------------------------------------------------------------------------
    | Query Logging
    |--------------------------------------------------------------------------
    */
    'log_queries' => env('DB_LOG_QUERIES', false),
    'slow_query_threshold' => env('DB_SLOW_QUERY_THRESHOLD', 1000), // milliseconds

    /*
    |--------------------------------------------------------------------------
    | Connection Pool Settings
    |--------------------------------------------------------------------------
    */
    'pool' => [
        'enabled' => env('DB_POOL_ENABLED', true),
        'min_connections' => env('DB_POOL_MIN', 5),
        'max_connections' => env('DB_POOL_MAX', 50),
        'idle_timeout' => env('DB_POOL_IDLE_TIMEOUT', 300), // seconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Read/Write Splitting
    |--------------------------------------------------------------------------
    */
    'read_write_splitting' => [
        'enabled' => env('DB_READ_WRITE_SPLITTING', false),
        'write_connection' => 'mysql',
        'read_connections' => ['mysql_read'],
        'sticky' => true, // Use write connection for reads after writes
    ],

    /*
    |--------------------------------------------------------------------------
    | Sharding Configuration
    |--------------------------------------------------------------------------
    */
    'sharding' => [
        'enabled' => env('DB_SHARDING_ENABLED', false),
        'strategy' => 'user_id', // user_id, hash, range
        'shards' => [
            'shard1' => [
                'connection' => 'mysql',
                'range' => [1, 1000000],
            ],
            'shard2' => [
                'connection' => 'mysql',
                'range' => [1000001, 2000000],
            ],
        ],
    ],
];
