<?php

/**
 * Email System Test Script
 * Tests email sending functionality
 */

// Define BASE_PATH
define('BASE_PATH', dirname(__DIR__));

// Load environment variables
if (file_exists(BASE_PATH . '/.env')) {
    $lines = file(BASE_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Load autoloader
require_once BASE_PATH . '/vendor/autoload.php';

use App\Modules\Auth\Services\EmailService;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobSpace Email Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="email"] { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>📧 JobSpace Email System Test</h1>
    
    <div class="info">
        <strong>Email Configuration:</strong><br>
        Host: <?= $_ENV['MAIL_HOST'] ?? 'Not set' ?><br>
        Port: <?= $_ENV['MAIL_PORT'] ?? 'Not set' ?><br>
        Username: <?= $_ENV['MAIL_USERNAME'] ?? 'Not set' ?><br>
        From: <?= $_ENV['MAIL_FROM_ADDRESS'] ?? 'Not set' ?><br>
        Password: <?= !empty($_ENV['MAIL_PASSWORD']) ? 'Set (****)' : 'Not set' ?>
    </div>
    
    <?php if (isset($_POST['test_email'])): ?>
        <h2>Test Results:</h2>
        
        <?php
        try {
            $emailService = new EmailService();
            $testEmail = $_POST['email'] ?? '<EMAIL>';
            $testOTP = '123456';
            
            echo '<div class="info">Testing email to: ' . htmlspecialchars($testEmail) . '</div>';
            
            // Test verification email
            $result = $emailService->sendVerificationEmail(
                $testEmail,
                'Test User',
                'test-token-123',
                $testOTP
            );
            
            if ($result) {
                echo '<div class="success">✅ Verification email sent successfully!</div>';
                echo '<div class="info">Check your email inbox (and spam folder) for the verification email.</div>';
                echo '<div class="info">Test OTP Code: <strong>' . $testOTP . '</strong></div>';
            } else {
                echo '<div class="error">❌ Failed to send verification email</div>';
            }
            
            // Test OTP email
            echo '<br><div class="info">Testing OTP email...</div>';
            $otpResult = $emailService->sendOTPEmail($testEmail, 'Test User', $testOTP);
            
            if ($otpResult) {
                echo '<div class="success">✅ OTP email sent successfully!</div>';
            } else {
                echo '<div class="error">❌ Failed to send OTP email</div>';
            }
            
            // Test welcome email
            echo '<br><div class="info">Testing welcome email...</div>';
            $welcomeResult = $emailService->sendWelcomeEmail($testEmail, 'Test User', [
                'username' => 'testuser',
                'role' => 'User',
                'referral_code_generated' => 'TEST123'
            ]);
            
            if ($welcomeResult) {
                echo '<div class="success">✅ Welcome email sent successfully!</div>';
            } else {
                echo '<div class="error">❌ Failed to send welcome email</div>';
            }
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Email test failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
        ?>
        
    <?php else: ?>
        
        <form method="POST">
            <div class="form-group">
                <label for="email">Test Email Address:</label>
                <input type="email" id="email" name="email" required 
                       placeholder="Enter your email address to test" 
                       value="<?= htmlspecialchars($_ENV['MAIL_USERNAME'] ?? '') ?>">
            </div>
            
            <button type="submit" name="test_email" class="btn">📧 Send Test Emails</button>
        </form>
        
        <div class="info">
            <strong>What this test will do:</strong><br>
            • Send a verification email with OTP<br>
            • Send a standalone OTP email<br>
            • Send a welcome email<br>
            • Show results and any errors
        </div>
        
    <?php endif; ?>
    
    <hr>
    <h2>📋 Email Logs</h2>
    <div class="info">
        Check your server's error log for email sending details. If SMTP is not configured, emails will be logged instead of sent.
    </div>
    
    <h2>🔧 Troubleshooting</h2>
    <div class="info">
        <strong>If emails are not being sent:</strong><br>
        1. Check your .env file for correct SMTP settings<br>
        2. Make sure Gmail "App Passwords" is enabled if using Gmail<br>
        3. Check server error logs for detailed error messages<br>
        4. Verify firewall allows outbound SMTP connections<br>
        5. Test with a different email provider if needed
    </div>
    
    <div class="info">
        <a href="/jobspace/auth/register">Go to Registration</a> | 
        <a href="/jobspace/test-auth.php">Test Auth System</a> | 
        <a href="/jobspace/migrate.php">Database Migration</a>
    </div>
    
    <hr>
    <p><small>JobSpace © 2024 - Email System Test</small></p>
</body>
</html>
