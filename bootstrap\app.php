<?php

/**
 * Application Bootstrap
 * Sets up the application environment and core services
 */

// Define env function if not exists
if (!function_exists('env')) {
    function env(string $key, $default = null) {
        $value = $_ENV[$key] ?? getenv($key);

        if ($value === false) {
            return $default;
        }

        // Convert string booleans
        if (in_array(strtolower($value), ['true', 'false'])) {
            return strtolower($value) === 'true';
        }

        // Convert string nulls
        if (strtolower($value) === 'null') {
            return null;
        }

        // Convert numeric strings
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? (float) $value : (int) $value;
        }

        return $value;
    }
}

// Error reporting for development
if (!defined('APP_DEBUG')) {
    define('APP_DEBUG', env('APP_DEBUG', true)); // Enable debug for now
}

if (APP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set timezone
date_default_timezone_set(env('APP_TIMEZONE', 'UTC'));

// Set memory limit for high-performance operations
ini_set('memory_limit', env('MEMORY_LIMIT', '512M'));

// Optimize for performance
ini_set('opcache.enable', 1);
ini_set('opcache.memory_consumption', 256);
ini_set('opcache.max_accelerated_files', 20000);
ini_set('opcache.revalidate_freq', 0);
ini_set('opcache.validate_timestamps', APP_DEBUG ? 1 : 0);

// Set up error and exception handlers
set_error_handler(function ($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    throw new ErrorException($message, 0, $severity, $file, $line);
});

set_exception_handler(function ($exception) {
    error_log("Uncaught Exception: " . $exception->getMessage());

    if (APP_DEBUG) {
        echo "<h1>Uncaught Exception</h1>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($exception->getMessage()) . "</p>";
        echo "<p><strong>File:</strong> " . htmlspecialchars($exception->getFile()) . "</p>";
        echo "<p><strong>Line:</strong> " . $exception->getLine() . "</p>";
        echo "<pre>" . htmlspecialchars($exception->getTraceAsString()) . "</pre>";
    } else {
        http_response_code(500);
        echo "Internal Server Error";
    }
});

// Register shutdown function for cleanup
register_shutdown_function(function () {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        error_log("Fatal Error: " . $error['message'] . " in " . $error['file'] . " on line " . $error['line']);

        if (!headers_sent()) {
            http_response_code(500);
        }

        if (APP_DEBUG) {
            echo "<h1>Fatal Error</h1>";
            echo "<p><strong>Message:</strong> " . htmlspecialchars($error['message']) . "</p>";
            echo "<p><strong>File:</strong> " . htmlspecialchars($error['file']) . "</p>";
            echo "<p><strong>Line:</strong> " . $error['line'] . "</p>";
        } else {
            echo "Internal Server Error";
        }
    }
});

// Load helper functions
if (file_exists(BASE_PATH . '/app/core/helpers.php')) {
    require_once BASE_PATH . '/app/core/helpers.php';
}


