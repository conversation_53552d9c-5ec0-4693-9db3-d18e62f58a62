<?php

// JobSpace Entry Point with Modular Architecture Support
define('BASE_PATH', dirname(__DIR__));

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load environment variables
if (file_exists(BASE_PATH . '/.env')) {
    $lines = file(BASE_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Load autoloader
require_once BASE_PATH . '/vendor/autoload.php';

// Load core classes
require_once BASE_PATH . '/app/core/Database.php';
require_once BASE_PATH . '/app/core/SessionManager.php';
require_once BASE_PATH . '/app/core/Security.php';
require_once BASE_PATH . '/app/core/ModuleManager.php';

// Initialize security and session
\App\Core\SessionManager::init();
\App\Core\Security::generateCSRFToken();

$moduleManager = new \App\Core\ModuleManager();

// Routing using Module System
$uri = $_SERVER['REQUEST_URI'] ?? '/';
$method = $_SERVER['REQUEST_METHOD'] ?? 'GET';

// Remove base path for XAMPP
$uri = str_replace('/jobspace', '', $uri);
$uri = parse_url($uri, PHP_URL_PATH);

// Check if any modules are available
if (!$moduleManager->hasAnyModules()) {
    // Show landing page if no modules available
    include BASE_PATH . '/resources/views/landing.php';
    exit;
}

// Get all available modules
$availableModules = $moduleManager->getAvailableModules();

// Load all module dependencies
foreach (array_keys($availableModules) as $moduleName) {
    $moduleManager->loadModuleDependencies($moduleName);
}

// Collect all routes from all modules
$allRoutes = [];
foreach (array_keys($availableModules) as $moduleName) {
    $moduleRoutes = $moduleManager->loadModuleRoutes($moduleName);
    if ($moduleRoutes) {
        $allRoutes = array_merge($allRoutes, $moduleRoutes);
    }
}

// Routes using Module System
if ($method === 'GET' || $method === 'POST') {
    // Rate limiting check
    $clientIP = \App\Core\Security::getClientIP();
    if (!\App\Core\Security::checkRateLimit($clientIP)) {
        http_response_code(429);
        echo json_encode(['error' => 'Too many requests. Please try again later.']);
        exit;
    }

    // CSRF validation for POST requests (except AJAX endpoints)
    if ($method === 'POST' && !str_contains($uri, '/api/') && !str_contains($uri, '/check-')) {
        if (!\App\Core\Security::validateCSRFFromRequest()) {
            http_response_code(403);
            echo json_encode(['error' => 'CSRF token validation failed']);
            exit;
        }
    }

    // Check if route exists in any module
    if (isset($allRoutes[$uri])) {
        $route = $allRoutes[$uri];
        if (is_array($route) && count($route) === 2) {
            [$controller, $methodName] = $route;
            echo $controller->$methodName();
        }
    } else {
        // Handle system routes (non-module routes)
        switch ($uri) {
            case '/test':
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'success',
                'message' => 'Framework is working perfectly!',
                'timestamp' => date('Y-m-d H:i:s'),
                'memory_usage' => memory_get_usage(true),
                'php_version' => PHP_VERSION,
                'framework' => 'JobSpace Modular Framework',
                'module' => $primaryModule
            ]);
            break;

        case '/api/status':
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'online',
                'version' => '1.0.0',
                'timestamp' => date('c'),
                'php_version' => PHP_VERSION,
                'framework' => 'JobSpace Modular Framework',
                'available_modules' => array_keys($availableModules),
                'total_routes' => count($allRoutes)
            ]);
            break;

        case '/api/health':
            header('Content-Type: application/json');
            echo json_encode([
                'status' => 'healthy',
                'timestamp' => date('c'),
                'memory_usage' => memory_get_usage(true),
                'modules_available' => $moduleManager->hasAnyModules(),
                'modules_count' => count($availableModules),
                'total_routes' => count($allRoutes)
            ]);
            break;

        case '/api/modules':
            header('Content-Type: application/json');
            echo json_encode([
                'modules' => $availableModules,
                'status' => $moduleManager->getModuleStatus(),
                'routes' => array_keys($allRoutes)
            ]);
            break;

        default:
            http_response_code(404);
            echo '<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Page Not Found</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <div class="bg-white p-8 rounded-lg shadow-md text-center">
            <h1 class="text-6xl font-bold text-red-500 mb-4">404</h1>
            <p class="text-gray-600 mb-4">Page not found</p>
            <a href="/jobspace/" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">Go Home</a>
        </div>
    </div>
</body>
</html>';
            break;
        }
    }
}
