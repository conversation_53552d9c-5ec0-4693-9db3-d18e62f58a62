<?php
/**
 * Auth Module Test File
 * Test the auth module functionality
 */

require_once __DIR__ . '/../../../bootstrap/app.php';

use App\Modules\Auth\Services\AuthHelper;
use App\Modules\Auth\Services\AuthService;
use App\Modules\Auth\Models\User;
use App\Core\SessionManager;

// Start session
SessionManager::init();

echo "<h1>Auth Module Test</h1>";

// Test 1: Check available roles
echo "<h2>1. Available Roles</h2>";
$availableRoles = User::getAvailableRoles();
echo "<pre>";
print_r($availableRoles);
echo "</pre>";

// Test 2: Check all roles
echo "<h2>2. All Roles</h2>";
$allRoles = User::getAllRoles();
echo "<pre>";
print_r($allRoles);
echo "</pre>";

// Test 3: Check role permissions
echo "<h2>3. Role Permissions</h2>";
foreach ($allRoles as $role) {
    echo "<h3>{$role} Role:</h3>";
    $permissions = User::getRolePermissions($role);
    echo "<pre>";
    print_r($permissions);
    echo "</pre>";
}

// Test 4: Check role configurations
echo "<h2>4. Role Configurations</h2>";
$authConfig = include __DIR__ . '/config/auth.php';
foreach ($authConfig['roles'] as $role => $config) {
    echo "<h3>{$role}:</h3>";
    echo "<ul>";
    echo "<li><strong>Title:</strong> {$config['title']}</li>";
    echo "<li><strong>Description:</strong> {$config['description']}</li>";
    echo "<li><strong>Icon:</strong> {$config['icon']}</li>";
    echo "<li><strong>Color:</strong> {$config['color']}</li>";
    echo "<li><strong>Redirect:</strong> {$config['redirect']}</li>";
    echo "<li><strong>Fallback:</strong> {$config['fallback_redirect']}</li>";
    echo "<li><strong>Permissions:</strong> " . implode(', ', $config['permissions']) . "</li>";
    echo "</ul>";
}

// Test 5: Check current authentication status
echo "<h2>5. Current Authentication Status</h2>";
echo "<p><strong>Is Logged In:</strong> " . (AuthHelper::isLoggedIn() ? 'Yes' : 'No') . "</p>";

if (AuthHelper::isLoggedIn()) {
    $user = AuthHelper::user();
    echo "<p><strong>Current User:</strong> {$user['username']} ({$user['role']})</p>";
    echo "<p><strong>Full Name:</strong> " . AuthHelper::getUserName() . "</p>";
    echo "<p><strong>Dashboard URL:</strong> " . AuthHelper::getDashboardUrl() . "</p>";
    
    echo "<h3>User Capabilities:</h3>";
    $capabilities = AuthHelper::getCapabilities();
    echo "<pre>";
    print_r($capabilities);
    echo "</pre>";
    
    echo "<h3>Role Checks:</h3>";
    echo "<ul>";
    echo "<li>Is Admin: " . (AuthHelper::isAdmin() ? 'Yes' : 'No') . "</li>";
    echo "<li>Is Creator: " . (AuthHelper::isCreator() ? 'Yes' : 'No') . "</li>";
    echo "<li>Is Business: " . (AuthHelper::isBusiness() ? 'Yes' : 'No') . "</li>";
    echo "<li>Is User: " . (AuthHelper::isUser() ? 'Yes' : 'No') . "</li>";
    echo "<li>Can Create Ads: " . (AuthHelper::canCreateAds() ? 'Yes' : 'No') . "</li>";
    echo "<li>Can Create Content: " . (AuthHelper::canCreateContent() ? 'Yes' : 'No') . "</li>";
    echo "<li>Can Manage Platform: " . (AuthHelper::canManagePlatform() ? 'Yes' : 'No') . "</li>";
    echo "</ul>";
} else {
    echo "<p>No user is currently logged in.</p>";
    echo "<p><a href='/jobspace/auth/login'>Login</a> | <a href='/jobspace/auth/register'>Register</a></p>";
}

// Test 6: Test role-specific actions
echo "<h2>6. Role-Specific Action Tests</h2>";

$testActions = [
    'browse_content' => 'Browse Content',
    'participate_quizzes' => 'Participate in Quizzes',
    'shop_products' => 'Shop Products',
    'apply_jobs' => 'Apply for Jobs',
    'social_features' => 'Use Social Features',
    'create_ads' => 'Create Advertisements',
    'manage_ads' => 'Manage Advertisements',
    'create_quizzes' => 'Create Quizzes',
    'create_products' => 'Create Products',
    'create_jobs' => 'Create Jobs',
    'create_content' => 'Create Content',
    'manage_users' => 'Manage Users',
    'manage_modules' => 'Manage Modules',
    'admin_access' => 'Admin Access'
];

if (AuthHelper::isLoggedIn()) {
    echo "<h3>Current User Can:</h3>";
    echo "<ul>";
    foreach ($testActions as $action => $label) {
        $canDo = AuthHelper::can($action);
        $status = $canDo ? '<span style="color: green;">✓</span>' : '<span style="color: red;">✗</span>';
        echo "<li>{$status} {$label}</li>";
    }
    echo "</ul>";
} else {
    echo "<p>Login to see action permissions.</p>";
}

// Test 7: Module configuration
echo "<h2>7. Module Configuration</h2>";
$moduleConfig = include __DIR__ . '/module.json';
echo "<pre>";
print_r($moduleConfig);
echo "</pre>";

// Test 8: Quick login links for testing (only in development)
if ($_ENV['APP_ENV'] === 'development' || true) {
    echo "<h2>8. Quick Test Login (Development Only)</h2>";
    echo "<p><em>These are for testing purposes only:</em></p>";
    echo "<ul>";
    echo "<li><a href='?test_login=admin'>Login as Admin</a></li>";
    echo "<li><a href='?test_login=creator'>Login as Creator</a></li>";
    echo "<li><a href='?test_login=business'>Login as Business</a></li>";
    echo "<li><a href='?test_login=user'>Login as User</a></li>";
    echo "<li><a href='?test_logout=1'>Logout</a></li>";
    echo "</ul>";
    
    // Handle test login
    if (isset($_GET['test_login'])) {
        $testRole = ucfirst($_GET['test_login']);
        if (in_array($testRole, User::getAllRoles())) {
            $testUser = [
                'id' => 999,
                'username' => 'test_' . strtolower($testRole),
                'email' => 'test_' . strtolower($testRole) . '@example.com',
                'first_name' => 'Test',
                'last_name' => $testRole,
                'role' => $testRole,
                'status' => 'active',
                'email_verified' => true
            ];
            
            SessionManager::login($testUser);
            echo "<script>window.location.reload();</script>";
        }
    }
    
    // Handle test logout
    if (isset($_GET['test_logout'])) {
        SessionManager::logout();
        echo "<script>window.location.reload();</script>";
    }
}

echo "<hr>";
echo "<p><strong>Auth Module Test Complete!</strong></p>";
echo "<p><a href='/jobspace/'>Back to Home</a></p>";
?>
