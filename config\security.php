<?php

return [
    /*
    |--------------------------------------------------------------------------
    | CSRF Protection
    |--------------------------------------------------------------------------
    */
    'csrf' => [
        'enabled' => env('CSRF_ENABLED', true),
        'token_name' => '_token',
        'header_name' => 'X-CSRF-TOKEN',
        'expire_time' => 7200, // 2 hours
        'regenerate_on_use' => false,
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limit' => [
        'enabled' => env('RATE_LIMIT_ENABLED', true),
        'max_attempts' => env('RATE_LIMIT_MAX_ATTEMPTS', 60),
        'decay_minutes' => env('RATE_LIMIT_DECAY_MINUTES', 1),
        'skip_successful_requests' => true,
        'headers' => [
            'limit' => 'X-RateLimit-Limit',
            'remaining' => 'X-RateLimit-Remaining',
            'reset' => 'X-RateLimit-Reset',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Password Requirements
    |--------------------------------------------------------------------------
    */
    'password' => [
        'min_length' => env('PASSWORD_MIN_LENGTH', 8),
        'max_length' => env('PASSWORD_MAX_LENGTH', 128),
        'require_uppercase' => env('PASSWORD_REQUIRE_UPPERCASE', true),
        'require_lowercase' => env('PASSWORD_REQUIRE_LOWERCASE', true),
        'require_numbers' => env('PASSWORD_REQUIRE_NUMBERS', true),
        'require_symbols' => env('PASSWORD_REQUIRE_SYMBOLS', false),
        'prevent_common' => env('PASSWORD_PREVENT_COMMON', true),
        'prevent_personal_info' => env('PASSWORD_PREVENT_PERSONAL_INFO', true),
        'history_limit' => env('PASSWORD_HISTORY_LIMIT', 5),
        'expire_days' => env('PASSWORD_EXPIRE_DAYS', 90),
    ],

    /*
    |--------------------------------------------------------------------------
    | Two-Factor Authentication
    |--------------------------------------------------------------------------
    */
    'two_factor' => [
        'enabled' => env('TWO_FACTOR_ENABLED', true),
        'issuer' => env('TWO_FACTOR_ISSUER', 'JobSpace'),
        'digits' => 6,
        'period' => 30,
        'algorithm' => 'sha1',
        'backup_codes_count' => 8,
        'recovery_codes_count' => 10,
    ],

    /*
    |--------------------------------------------------------------------------
    | Account Lockout
    |--------------------------------------------------------------------------
    */
    'lockout' => [
        'enabled' => env('ACCOUNT_LOCKOUT_ENABLED', true),
        'max_attempts' => env('LOCKOUT_MAX_ATTEMPTS', 5),
        'lockout_duration' => env('LOCKOUT_DURATION', 900), // 15 minutes
        'reset_after_success' => true,
        'notify_user' => true,
        'notify_admin' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | IP Whitelist/Blacklist
    |--------------------------------------------------------------------------
    */
    'ip_filtering' => [
        'enabled' => env('IP_FILTERING_ENABLED', false),
        'whitelist' => env('IP_WHITELIST', []),
        'blacklist' => env('IP_BLACKLIST', []),
        'auto_blacklist' => [
            'enabled' => true,
            'threshold' => 100, // requests per minute
            'duration' => 3600, // 1 hour
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Content Security Policy
    |--------------------------------------------------------------------------
    */
    'csp' => [
        'enabled' => env('CSP_ENABLED', true),
        'report_only' => env('CSP_REPORT_ONLY', false),
        'report_uri' => env('CSP_REPORT_URI', '/csp-report'),
        'directives' => [
            'default-src' => "'self'",
            'script-src' => "'self' 'unsafe-inline' 'unsafe-eval' https://cdn.tailwindcss.com",
            'style-src' => "'self' 'unsafe-inline' https://fonts.googleapis.com",
            'img-src' => "'self' data: https:",
            'font-src' => "'self' https://fonts.gstatic.com",
            'connect-src' => "'self'",
            'media-src' => "'self'",
            'object-src' => "'none'",
            'child-src' => "'self'",
            'frame-ancestors' => "'none'",
            'form-action' => "'self'",
            'base-uri' => "'self'",
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | HTTP Security Headers
    |--------------------------------------------------------------------------
    */
    'headers' => [
        'x-frame-options' => 'DENY',
        'x-content-type-options' => 'nosniff',
        'x-xss-protection' => '1; mode=block',
        'referrer-policy' => 'strict-origin-when-cross-origin',
        'permissions-policy' => 'geolocation=(), microphone=(), camera=()',
        'strict-transport-security' => 'max-age=31536000; includeSubDomains',
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Security
    |--------------------------------------------------------------------------
    */
    'file_upload' => [
        'max_size' => env('UPLOAD_MAX_SIZE', 10485760), // 10MB
        'allowed_extensions' => [
            'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
            'documents' => ['pdf', 'doc', 'docx', 'txt', 'rtf'],
            'archives' => ['zip', 'rar', '7z', 'tar', 'gz'],
            'videos' => ['mp4', 'avi', 'mov', 'wmv', 'flv'],
            'audio' => ['mp3', 'wav', 'ogg', 'flac'],
        ],
        'scan_for_viruses' => env('UPLOAD_VIRUS_SCAN', false),
        'quarantine_suspicious' => true,
        'validate_mime_type' => true,
        'check_file_signature' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Input Validation
    |--------------------------------------------------------------------------
    */
    'input_validation' => [
        'max_input_vars' => 1000,
        'max_input_nesting_level' => 64,
        'max_input_time' => 60,
        'strip_tags' => true,
        'filter_control_characters' => true,
        'normalize_unicode' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | SQL Injection Protection
    |--------------------------------------------------------------------------
    */
    'sql_injection' => [
        'use_prepared_statements' => true,
        'validate_input' => true,
        'log_suspicious_queries' => true,
        'block_dangerous_functions' => true,
        'patterns' => [
            '/(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)/i',
            '/\b(select|insert|update|delete|drop|create|alter)\b.*\b(from|into|table|database)\b/i',
            '/\b(exec|execute|sp_|xp_)\b/i',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | XSS Protection
    |--------------------------------------------------------------------------
    */
    'xss_protection' => [
        'filter_input' => true,
        'escape_output' => true,
        'allowed_tags' => '<p><br><strong><em><u><a><ul><ol><li><h1><h2><h3><h4><h5><h6>',
        'allowed_attributes' => ['href', 'title', 'alt', 'class', 'id'],
        'purify_html' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Security
    |--------------------------------------------------------------------------
    */
    'session_security' => [
        'regenerate_id' => true,
        'regenerate_interval' => 1800, // 30 minutes
        'validate_ip' => env('SESSION_VALIDATE_IP', false),
        'validate_user_agent' => true,
        'encrypt_data' => env('SESSION_ENCRYPT', false),
        'secure_cookies' => env('SESSION_SECURE_COOKIE', false),
        'httponly_cookies' => true,
        'samesite_cookies' => 'Lax',
    ],

    /*
    |--------------------------------------------------------------------------
    | API Security
    |--------------------------------------------------------------------------
    */
    'api_security' => [
        'rate_limit_per_minute' => 60,
        'require_authentication' => true,
        'allowed_origins' => env('API_ALLOWED_ORIGINS', ['*']),
        'allowed_methods' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
        'allowed_headers' => ['Content-Type', 'Authorization', 'X-Requested-With'],
        'max_request_size' => '10M',
        'timeout' => 30,
    ],

    /*
    |--------------------------------------------------------------------------
    | Encryption Settings
    |--------------------------------------------------------------------------
    */
    'encryption' => [
        'cipher' => 'AES-256-CBC',
        'key_rotation' => [
            'enabled' => false,
            'interval' => 2592000, // 30 days
            'keep_old_keys' => 3,
        ],
        'hash_algorithm' => 'sha256',
        'password_hash' => 'argon2id',
        'password_hash_options' => [
            'memory_cost' => 65536, // 64 MB
            'time_cost' => 4,       // 4 iterations
            'threads' => 3,         // 3 threads
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Monitoring
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'enabled' => env('SECURITY_MONITORING_ENABLED', true),
        'log_failed_logins' => true,
        'log_privilege_escalation' => true,
        'log_suspicious_activity' => true,
        'alert_threshold' => 10, // alerts per hour
        'notification_channels' => ['email', 'slack'],
    ],
];
