<?php

namespace App\Modules\Auth\Middleware;

use App\Modules\Auth\Services\AuthHelper;
use App\Core\SessionManager;

class RoleMiddleware
{
    /**
     * Handle role-based access control
     */
    public static function handle(array $allowedRoles = [], string $redirectUrl = '/jobspace/auth/login'): void
    {
        // Check if user is authenticated
        if (!AuthHelper::isLoggedIn()) {
            SessionManager::flash('login_error', 'Please login to access this page');
            header("Location: {$redirectUrl}");
            exit;
        }

        // If no specific roles required, just authentication is enough
        if (empty($allowedRoles)) {
            return;
        }

        // Check if user has required role
        if (!AuthHelper::hasAnyRole($allowedRoles)) {
            SessionManager::flash('error', 'You do not have permission to access this page');
            AuthHelper::redirectToDashboard();
        }
    }

    /**
     * Require admin access
     */
    public static function requireAdmin(): void
    {
        self::handle(['Admin']);
    }

    /**
     * Require creator access
     */
    public static function requireCreator(): void
    {
        self::handle(['Creator', 'Admin']);
    }

    /**
     * Require business access
     */
    public static function requireBusiness(): void
    {
        self::handle(['Business', 'Admin']);
    }

    /**
     * Require ad creation permission
     */
    public static function requireAdPermission(): void
    {
        self::handle(['Business', 'Admin']);
    }

    /**
     * Require content creation permission
     */
    public static function requireContentPermission(): void
    {
        self::handle(['Creator', 'Admin']);
    }

    /**
     * Require user access (any authenticated user)
     */
    public static function requireUser(): void
    {
        self::handle(['User', 'Business', 'Creator', 'Admin']);
    }

    /**
     * Check specific capability
     */
    public static function requireCapability(string $capability): void
    {
        if (!AuthHelper::isLoggedIn()) {
            SessionManager::flash('login_error', 'Please login to access this page');
            header('Location: /jobspace/auth/login');
            exit;
        }

        if (!AuthHelper::can($capability)) {
            SessionManager::flash('error', 'You do not have permission to perform this action');
            AuthHelper::redirectToDashboard();
        }
    }

    /**
     * Guest only (not authenticated)
     */
    public static function guestOnly(string $redirectUrl = null): void
    {
        if (AuthHelper::isLoggedIn()) {
            $redirectUrl = $redirectUrl ?? AuthHelper::getDashboardUrl();
            header("Location: {$redirectUrl}");
            exit;
        }
    }

    /**
     * Role-based page access control
     */
    public static function checkPageAccess(string $page): void
    {
        $pageRoles = [
            // Admin pages
            'admin_dashboard' => ['Admin'],
            'user_management' => ['Admin'],
            'module_management' => ['Admin'],
            'system_settings' => ['Admin'],
            
            // Creator pages
            'creator_dashboard' => ['Creator', 'Admin'],
            'create_quiz' => ['Creator', 'Admin'],
            'create_product' => ['Creator', 'Admin'],
            'create_job' => ['Creator', 'Admin'],
            'content_management' => ['Creator', 'Admin'],
            
            // Business pages
            'business_dashboard' => ['Business', 'Admin'],
            'create_ad' => ['Business', 'Admin'],
            'ad_management' => ['Business', 'Admin'],
            'ad_analytics' => ['Business', 'Admin'],
            
            // User pages
            'user_dashboard' => ['User', 'Business', 'Creator', 'Admin'],
            'feed' => ['User', 'Business', 'Creator', 'Admin'],
            'quiz_participate' => ['User', 'Creator', 'Admin'],
            'shop' => ['User', 'Creator', 'Admin'],
            'job_apply' => ['User', 'Creator', 'Admin'],
            'social' => ['User', 'Creator', 'Admin'],
            
            // Public pages (no authentication required)
            'home' => [],
            'about' => [],
            'contact' => [],
            'help' => [],
            'login' => [],
            'register' => [],
        ];

        $requiredRoles = $pageRoles[$page] ?? ['User', 'Business', 'Creator', 'Admin'];
        
        // If no roles required, it's a public page
        if (empty($requiredRoles)) {
            return;
        }

        self::handle($requiredRoles);
    }

    /**
     * API endpoint protection
     */
    public static function protectAPI(array $allowedRoles = []): void
    {
        header('Content-Type: application/json');

        if (!AuthHelper::isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Authentication required']);
            exit;
        }

        if (!empty($allowedRoles) && !AuthHelper::hasAnyRole($allowedRoles)) {
            http_response_code(403);
            echo json_encode(['error' => 'Insufficient permissions']);
            exit;
        }
    }

    /**
     * Module access control
     */
    public static function checkModuleAccess(string $module): void
    {
        $moduleRoles = [
            'admin' => ['Admin'],
            'creator' => ['Creator', 'Admin'],
            'business' => ['Business', 'Admin'],
            'user' => ['User', 'Business', 'Creator', 'Admin'],
            'feed' => ['User', 'Business', 'Creator', 'Admin'],
            'quiz' => ['User', 'Creator', 'Admin'],
            'ecommerce' => ['User', 'Creator', 'Admin'],
            'freelance' => ['User', 'Creator', 'Admin'],
            'social' => ['User', 'Creator', 'Admin'],
            'ads' => ['Business', 'Admin'],
            'wallet' => ['User', 'Business', 'Creator', 'Admin'],
            'dashboard' => ['User', 'Business', 'Creator', 'Admin'],
        ];

        $requiredRoles = $moduleRoles[$module] ?? ['Admin'];
        self::handle($requiredRoles);
    }

    /**
     * Feature flag based access
     */
    public static function checkFeatureAccess(string $feature): void
    {
        $featureRoles = [
            'create_ads' => ['Business', 'Admin'],
            'create_quizzes' => ['Creator', 'Admin'],
            'create_products' => ['Creator', 'Admin'],
            'create_jobs' => ['Creator', 'Admin'],
            'manage_users' => ['Admin'],
            'manage_modules' => ['Admin'],
            'view_analytics' => ['Business', 'Creator', 'Admin'],
            'moderate_content' => ['Admin'],
            'manage_payments' => ['Admin'],
        ];

        $requiredRoles = $featureRoles[$feature] ?? ['Admin'];
        self::handle($requiredRoles);
    }
}
