<?php

/**
 * Web-based Database Migration Script
 * Access via: http://localhost/jobspace/migrate.php
 */

// Define BASE_PATH
define('BASE_PATH', dirname(__DIR__));

// Load environment variables
if (file_exists(BASE_PATH . '/.env')) {
    $lines = file(BASE_PATH . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

// Load autoloader
require_once BASE_PATH . '/vendor/autoload.php';

// Load core classes
require_once BASE_PATH . '/app/core/Database.php';

use App\Core\Database;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JobSpace Database Migration</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
        .success { color: #28a745; background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .error { color: #dc3545; background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .info { color: #17a2b8; background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .btn { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🚀 JobSpace Database Migration</h1>
    
    <?php if (isset($_POST['migrate'])): ?>
        <h2>Migration Results:</h2>
        
        <?php
        try {
            echo '<div class="info">Starting database migration...</div>';
            
            // Create tables using Database class
            Database::createTables();
            
            echo '<div class="success">✅ Database migration completed successfully!</div>';
            
            echo '<div class="info">';
            echo '<strong>Created tables:</strong><br>';
            echo '- users<br>';
            echo '- verification_tokens<br>';
            echo '- password_resets<br>';
            echo '- sessions<br>';
            echo '- login_attempts<br>';
            echo '</div>';
            
            // Check if admin user exists
            try {
                $adminExists = Database::fetch("SELECT COUNT(*) as count FROM users WHERE role = 'Admin'");
                
                if (($adminExists['count'] ?? 0) == 0) {
                    echo '<div class="info">ℹ️ No admin user found. You can create an admin account during registration.</div>';
                } else {
                    echo '<div class="success">✅ Admin user already exists.</div>';
                }
            } catch (Exception $e) {
                echo '<div class="info">ℹ️ Could not check for admin user (table may be empty).</div>';
            }
            
            echo '<div class="success">🎉 Database is ready for use!</div>';
            echo '<div class="info"><a href="/jobspace/auth/register">Go to Registration</a> | <a href="/jobspace/auth/login">Go to Login</a></div>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Migration failed: ' . htmlspecialchars($e->getMessage()) . '</div>';
            echo '<div class="info">Please check your database configuration and make sure your database server is running.</div>';
        }
        ?>
        
    <?php else: ?>
        
        <div class="info">
            <strong>Before running migration:</strong><br>
            1. Make sure XAMPP MySQL is running<br>
            2. Create a database named 'jobspace' in phpMyAdmin<br>
            3. Check your database configuration in .env file
        </div>
        
        <h2>Current Database Configuration:</h2>
        <pre>
Host: <?= $_ENV['DB_HOST'] ?? 'localhost' ?>
Port: <?= $_ENV['DB_PORT'] ?? '3306' ?>
Database: <?= $_ENV['DB_DATABASE'] ?? 'jobspace' ?>
Username: <?= $_ENV['DB_USERNAME'] ?? 'root' ?>
Password: <?= empty($_ENV['DB_PASSWORD']) ? '(empty)' : '***' ?>
        </pre>
        
        <form method="POST">
            <button type="submit" name="migrate" class="btn">🚀 Run Database Migration</button>
        </form>
        
    <?php endif; ?>
    
    <hr>
    <p><small>JobSpace © 2024 - Database Migration Tool</small></p>
</body>
</html>
