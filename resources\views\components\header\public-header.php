<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'JobSpace - High-Performance Platform' ?></title>
    <meta name="description" content="<?= $description ?? 'JobSpace - High-Performance Platform for Quiz, Social Media, E-commerce & Freelancing' ?>">
    <meta name="keywords" content="<?= $keywords ?? 'jobspace, platform, quiz, social media, ecommerce, freelancing' ?>">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Custom Styles -->
    <style>
        /* Mobile responsive adjustments */
        @media (max-width: 640px) {
            .mobile-responsive {
                padding: 1rem;
            }
        }

        /* Success/Error message animations */
        .alert-slide-in {
            animation: slideInFromTop 0.5s ease-out;
        }

        @keyframes slideInFromTop {
            0% {
                transform: translateY(-100%);
                opacity: 0;
            }
            100% {
                transform: translateY(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body class="<?= $bodyClass ?? 'bg-gray-50' ?>">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/jobspace/" class="text-xl font-bold text-gray-800">
                        🚀 JobSpace
                    </a>
                </div>
                <div class="flex items-center space-x-4">
                    <?php if (isset($navigation)): ?>
                        <?php foreach ($navigation as $nav): ?>
                            <a href="<?= htmlspecialchars($nav['url']) ?>"
                               class="<?= $nav['active'] ? 'text-blue-600 font-semibold' : 'text-gray-600 hover:text-gray-900' ?>">
                                <?= htmlspecialchars($nav['title']) ?>
                            </a>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <a href="/jobspace/" class="text-gray-600 hover:text-gray-900">Home!</a>
                        <a href="/jobspace/about" class="text-gray-600 hover:text-gray-900">About</a>
                        <a href="/jobspace/contact" class="text-gray-600 hover:text-gray-900">Contact</a>
                        <a href="/jobspace/terms" class="text-gray-600 hover:text-gray-900">Terms</a>
                        <a href="/jobspace/privacy" class="text-gray-600 hover:text-gray-900">Privacy</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </nav>
